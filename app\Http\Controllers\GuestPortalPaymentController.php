<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Hotel;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;

class GuestPortalPaymentController extends Controller
{
    /**
     * Show payment options for an invoice
     */
    public function showPayment($hotelSlug, $invoiceId)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        $invoice = Invoice::where('hotel_id', $hotel->id)
            ->where('id', $invoiceId)
            ->with(['booking.room.roomType', 'additionalCharges', 'payments'])
            ->firstOrFail();

        // Check if invoice is already paid
        if ($invoice->status === 'paid') {
            return view('guest-portal.payment-complete', compact('hotel', 'invoice'));
        }

        return view('guest-portal.payment', compact('hotel', 'invoice'));
    }

    /**
     * Process PayPal payment
     */
    public function paypalPayment($hotelSlug, $invoiceId)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        $invoice = Invoice::where('hotel_id', $hotel->id)
            ->where('id', $invoiceId)
            ->with(['booking', 'additionalCharges'])
            ->firstOrFail();

        // Check if payment is already made
        $existingPayment = Payment::where('invoice_id', $invoice->id)->first();
        if ($existingPayment) {
            return redirect()->route('guest-portal.payment-complete', [$hotelSlug, $invoiceId])
                ->with('error', 'Payment already made for this invoice.');
        }

        return view('guest-portal.paypal-checkout', compact('hotel', 'invoice'));
    }

    /**
     * Process PayMongo payment
     */
    public function paymongoPayment($hotelSlug, $invoiceId)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        $invoice = Invoice::where('hotel_id', $hotel->id)
            ->where('id', $invoiceId)
            ->with(['booking', 'additionalCharges'])
            ->firstOrFail();

        // Check if payment is already made
        $existingPayment = Payment::where('invoice_id', $invoice->id)->first();
        if ($existingPayment) {
            return redirect()->route('guest-portal.payment-complete', [$hotelSlug, $invoiceId])
                ->with('error', 'Payment already made for this invoice.');
        }

        // Clear any existing checkout session
        Session::forget('guest_checkout_session_id');

        // Calculate service fee
        $serviceFeePercentage = get_setting('paymongo_service_fee', 0) / 100;
        $serviceFeeInCentavos = $invoice->total_amount * $serviceFeePercentage * 100;

        // Prepare line items
        $lineItems = [];
        
        // Add room charge
        if ($invoice->booking) {
            $lineItems[] = [
                'currency' => 'PHP',
                'amount' => $invoice->booking->total_price * 100,
                'description' => 'Room: ' . $invoice->booking->room->room_number,
                'name' => 'Hotel Room Booking',
                'quantity' => 1,
            ];
        }

        // Add additional charges
        foreach ($invoice->additionalCharges as $charge) {
            $lineItems[] = [
                'currency' => 'PHP',
                'amount' => $charge->amount * 100,
                'description' => $charge->description,
                'name' => 'Additional Charge',
                'quantity' => 1,
            ];
        }

        // Add service fee if applicable
        if ($serviceFeeInCentavos > 0) {
            $lineItems[] = [
                'currency' => 'PHP',
                'amount' => $serviceFeeInCentavos,
                'description' => 'Payment processing fee',
                'name' => 'Service Fee',
                'quantity' => 1,
            ];
        }

        $data = [
            'data' => [
                'attributes' => [
                    'line_items' => $lineItems,
                    'payment_method_types' => ['card', 'paymaya', 'grab_pay', 'gcash', 'qrph'],
                    'success_url' => route('guest-portal.payment-success', [$hotelSlug, $invoice->id]),
                    'cancel_url' => route('guest-portal.payment-cancel', [$hotelSlug, $invoice->id]),
                    'description' => 'Payment for Invoice #' . $invoice->id . ' - ' . $hotel->name,
                ]
            ]
        ];

        $response = Curl::to('https://api.paymongo.com/v1/checkout_sessions')
            ->withHeader('Content-Type: application/json')
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . base64_encode(env('PAYMONGO_SECRET_KEY') . ':'))
            ->withData($data)
            ->asJson()
            ->post();

        Log::info('Guest Portal PayMongo response: ', (array) $response);
        
        if (isset($response->data)) {
            // Save the checkout session ID
            Session::put('guest_checkout_session_id', $response->data->id);
            Session::put('guest_checkout_invoice_id', $invoice->id);

            // Redirect to PayMongo checkout URL
            return redirect()->to($response->data->attributes->checkout_url);
        } else {
            return back()->withErrors(['msg' => 'Failed to create checkout session. Please try again.']);
        }
    }

    /**
     * Handle PayMongo payment success
     */
    public function paymongoSuccess($hotelSlug, $invoiceId)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        $invoice = Invoice::where('hotel_id', $hotel->id)
            ->where('id', $invoiceId)
            ->firstOrFail();

        // Get the checkout session ID from the session
        $checkoutSessionId = Session::get('guest_checkout_session_id');

        if (!$checkoutSessionId) {
            return redirect()->route('guest-portal.payment', [$hotelSlug, $invoiceId])
                ->with('error', 'Payment session not found.');
        }

        // Get the payment details from PayMongo
        $response = Curl::to('https://api.paymongo.com/v1/checkout_sessions/' . $checkoutSessionId)
            ->withHeader('accept: application/json')
            ->withHeader('Authorization: Basic ' . base64_encode(env('PAYMONGO_SECRET_KEY') . ':'))
            ->asJson()
            ->get();

        if (isset($response->data) && $response->data->attributes->payment_intent->attributes->status === 'succeeded') {
            // Check if payment already exists
            $existingPayment = Payment::where('invoice_id', $invoice->id)->first();
            
            if (!$existingPayment) {
                // Create payment record
                Payment::create([
                    'hotel_id' => $hotel->id,
                    'invoice_id' => $invoice->id,
                    'amount' => $invoice->total_amount,
                    'payment_method' => 'paymongo',
                    'payment_date' => now(),
                    'created_by' => 'Guest Portal',
                    'is_remitted' => 'no',
                ]);

                // Update invoice status
                $invoice->update(['status' => 'paid']);

                // Log activity
                log_activity('Guest Portal Payment completed for Invoice ID: ' . $invoice->id, 'Guest Portal Payment');
            }

            // Clear session
            Session::forget(['guest_checkout_session_id', 'guest_checkout_invoice_id']);

            return redirect()->route('guest-portal.payment-complete', [$hotelSlug, $invoiceId])
                ->with('success', 'Payment completed successfully!');
        }

        return redirect()->route('guest-portal.payment', [$hotelSlug, $invoiceId])
            ->with('error', 'Payment was not successful. Please try again.');
    }

    /**
     * Handle PayMongo payment cancellation
     */
    public function paymongoCancel($hotelSlug, $invoiceId)
    {
        // Clear session
        Session::forget(['guest_checkout_session_id', 'guest_checkout_invoice_id']);

        return redirect()->route('guest-portal.payment', [$hotelSlug, $invoiceId])
            ->with('info', 'Payment was cancelled.');
    }

    /**
     * Show payment completion page
     */
    public function paymentComplete($hotelSlug, $invoiceId)
    {
        $hotel = Hotel::where('slug', $hotelSlug)->firstOrFail();
        
        $invoice = Invoice::where('hotel_id', $hotel->id)
            ->where('id', $invoiceId)
            ->with(['booking.room.roomType', 'payments'])
            ->firstOrFail();

        return view('guest-portal.payment-complete', compact('hotel', 'invoice'));
    }

    /**
     * Generate QR code for guest payment
     */
    public function generatePaymentQR($invoiceId)
    {
        $invoice = Invoice::with('booking')->findOrFail($invoiceId);
        $hotel = $invoice->hotel;
        
        // Generate payment URL
        $paymentUrl = route('guest-portal.payment', [$hotel->slug, $invoice->id]);
        
        // Generate QR code
        $qrCode = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(300)->generate($paymentUrl);
        
        return response($qrCode)->header('Content-Type', 'image/png');
    }
}
